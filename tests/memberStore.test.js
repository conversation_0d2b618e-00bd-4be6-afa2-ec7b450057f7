/**
 * Member Store Tests
 * 
 * Tests for member selection functionality and state management
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { get } from 'svelte/store';
import {
  selectedMemberStore,
  selectedMemberCode,
  selectedMemberDisplayName,
  memberOptions,
  selectMember,
  clearMemberSelection,
  getCurrentSelectedMember,
  hasMemberSelected,
  loadMemberList
} from '../src/stores/memberStore.js';

import {
  getAllMembers,
  getMemberByCode,
  getMemberDisplayName,
  getDefaultMember
} from '../src/utils/memberData.js';

// Mock sessionStorage
const mockSessionStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn()
};

Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage
});

describe('Member Store', () => {
  beforeEach(() => {
    // Clear all mocks
    vi.clearAllMocks();

    // Reset stores
    clearMemberSelection();
  });

  describe('Member Data Utilities', () => {
    it('should get all active members', () => {
      const members = getAllMembers(false);
      expect(members).toBeDefined();
      expect(Array.isArray(members)).toBe(true);
      expect(members.length).toBeGreaterThan(0);

      // All members should be active
      members.forEach(member => {
        expect(member.memberStatus).toBe('Active');
      });
    });

    it('should get member by code', () => {
      const member = getMemberByCode('MEM001');
      expect(member).toBeDefined();
      expect(member.memberCode).toBe('MEM001');
      expect(member.nameEN).toBe('Somchai');
    });

    it('should return null for invalid member code', () => {
      const member = getMemberByCode('INVALID');
      expect(member).toBeNull();
    });

    it('should get member display name in Thai consistently', () => {
      const member = getMemberByCode('MEM001');
      const displayName = getMemberDisplayName(member, 'EN'); // Should still return Thai
      expect(displayName).toBe('นายสมชาย ใจดี');

      const displayNameTH = getMemberDisplayName(member, 'TH');
      expect(displayNameTH).toBe('นายสมชาย ใจดี');
    });

    it('should get default member', () => {
      const defaultMember = getDefaultMember();
      expect(defaultMember).toBeDefined();
      expect(defaultMember.memberStatus).toBe('Active');
    });
  });

  describe('Member Selection Store', () => {
    it('should initialize with default member', async () => {
      // Load member list first
      await loadMemberList();

      // Select default member since we cleared it in beforeEach
      const defaultMember = getDefaultMember();
      selectMember(defaultMember.memberCode);

      const currentMember = getCurrentSelectedMember();
      expect(currentMember).toBeDefined();
      expect(currentMember.memberStatus).toBe('Active');
    });

    it('should select a member', () => {
      selectMember('MEM003');

      const selectedMember = get(selectedMemberStore);
      expect(selectedMember).toBeDefined();
      expect(selectedMember.memberCode).toBe('MEM003');
      expect(selectedMember.nameEN).toBe('Malee');
    });

    it('should update derived stores when member is selected', () => {
      selectMember('MEM003');

      const memberCode = get(selectedMemberCode);
      const displayName = get(selectedMemberDisplayName);

      expect(memberCode).toBe('MEM003');
      // MEM003 has language preference 'TH', so display name will be in Thai
      expect(displayName).toContain('มาลี');
    });

    it('should clear member selection', () => {
      selectMember('MEM003');
      expect(hasMemberSelected()).toBe(true);

      clearMemberSelection();
      expect(hasMemberSelected()).toBe(false);

      const selectedMember = get(selectedMemberStore);
      expect(selectedMember).toBeNull();
    });

    it('should handle invalid member selection gracefully', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => { });

      selectMember('INVALID_CODE');

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Member with code INVALID_CODE not found')
      );

      consoleSpy.mockRestore();
    });

    it('should persist selection to session storage', () => {
      selectMember('MEM003');

      expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
        'insurance_portal_selected_member',
        expect.stringContaining('MEM003')
      );
    });

    it('should clear session storage when selection is cleared', () => {
      clearMemberSelection();

      expect(mockSessionStorage.removeItem).toHaveBeenCalledWith(
        'insurance_portal_selected_member'
      );
    });
  });

  describe('Member Options', () => {
    it('should generate member options for dropdown', async () => {
      await loadMemberList();

      const options = get(memberOptions);
      expect(Array.isArray(options)).toBe(true);
      expect(options.length).toBeGreaterThan(0);

      // Check option structure
      const firstOption = options[0];
      expect(firstOption).toHaveProperty('value');
      expect(firstOption).toHaveProperty('label');
      expect(firstOption).toHaveProperty('shortLabel');
      expect(firstOption).toHaveProperty('status');
      expect(firstOption).toHaveProperty('vip');
      expect(firstOption).toHaveProperty('cardType');
      expect(firstOption).toHaveProperty('member');
    });

    it('should include VIP status in options', async () => {
      await loadMemberList();

      const options = get(memberOptions);
      const vipOption = options.find(option => option.vip === true);

      expect(vipOption).toBeDefined();
      expect(vipOption.member.vip).toBe('Y');
    });
  });

  describe('Member List Loading', () => {
    it('should load member list successfully', async () => {
      const members = await loadMemberList();

      expect(Array.isArray(members)).toBe(true);
      expect(members.length).toBeGreaterThan(0);

      // All loaded members should be active
      members.forEach(member => {
        expect(member.memberStatus).toBe('Active');
      });
    });

    it('should not reload if data already exists', async () => {
      // First load
      await loadMemberList();

      // Second load should use cached data
      const startTime = Date.now();
      await loadMemberList();
      const endTime = Date.now();

      // Should be very fast (cached)
      expect(endTime - startTime).toBeLessThan(50);
    });

    it('should force refresh when requested', async () => {
      // First load
      await loadMemberList();

      // Force refresh
      const members = await loadMemberList(true);

      expect(Array.isArray(members)).toBe(true);
      expect(members.length).toBeGreaterThan(0);
    });
  });
});
