/**
 * TPA API Client - Main Export Module
 * 
 * This module provides the main API interface for the TPA (Third Party Administrator)
 * integration. It exports all the necessary functions and utilities for interacting
 * with the TPA API endpoints.
 */

// Core client and configuration
export { TpaApiClient, defaultClient, get, healthCheck } from './client.js';

// Import endpoint wrapper functions for local use and re-export
import {
  searchPolicies,
  getPolicyDetail,
  getClaimsList,
  checkHealth,
  getApiInfo,
  searchPoliciesByCitizenId,
  searchPoliciesByPolicyAndName,
  searchPoliciesByName,
  getClaimsByCitizenId,
  getClaimsByMemberCode,
  getMemberData,
  getAllMembersData,
  getMemberPolicies,
  getMemberClaims
} from './endpoints.js';

// Re-export endpoint functions
export {
  searchPolicies,
  getPolicyDetail,
  getClaimsList,
  checkHealth,
  getApiInfo,
  searchPoliciesByCitizenId,
  searchPoliciesByPolicyAndName,
  searchPoliciesByName,
  getClaimsByCitizenId,
  getClaimsByMemberCode,
  getMemberData,
  getAllMembersData,
  getMemberPolicies,
  getMemberClaims
};

// Error classes and utilities
export {
  ApiError,
  NetworkError,
  TimeoutError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  RateLimitError,
  createError,
  handleError,
  logError
} from './errors.js';

// Type definitions and constants
export {
  PolicyListParams,
  ClaimListParams,
  ValidValues,
  PolicyStatus,
  ClaimStatus,
  MemberType,
  CardType,
  Language,
  VipStatus,
  TypeCheckers,
  ResponseStructures,
  Defaults
} from './types.js';

// Validation utilities
export {
  validatePolicyListParams,
  validateClaimListParams,
  validatePolicyDetailParams,
  cleanParameters,
  findMatchingCombination,
  validateParameterValues,
  createValidationError,
  validateOrThrow
} from '../utils/validation.js';

// Data formatting utilities
export {
  formatPolicy,
  formatClaim,
  formatPolicyDetail,
  formatBenefit,
  formatCondition,
  formatCurrency
} from '../utils/formatters.js';

/**
 * Quick start API client with common configurations
 */
export const api = {
  // Policy operations
  policies: {
    search: searchPolicies,
    getDetail: getPolicyDetail,
    searchByCitizenId: searchPoliciesByCitizenId,
    searchByName: searchPoliciesByName,
    searchByPolicyAndName: searchPoliciesByPolicyAndName
  },

  // Claim operations
  claims: {
    getList: getClaimsList,
    getByCitizenId: getClaimsByCitizenId,
    getByMemberCode: getClaimsByMemberCode
  },

  // Member operations
  members: {
    getAll: getAllMembersData,
    getData: getMemberData,
    getPolicyDetail: getPolicyDetail,
    getClaims: getClaimsByMemberCode,
    getPolicies: getMemberPolicies,
    getClaimsData: getMemberClaims
  },

  // System operations
  system: {
    health: checkHealth,
    info: getApiInfo
  }
};

/**
 * Default export for convenience
 */
export default api;
