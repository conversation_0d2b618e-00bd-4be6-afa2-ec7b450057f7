<!--
  MemberSelector Component
  
  A responsive dropdown component for selecting members with the following features:
  - Accessible design with ARIA labels and keyboard navigation
  - Search/filter functionality
  - VIP status indicators
  - Card type display
  - Responsive design with Tailwind CSS
  - Session persistence
-->

<script>
  import { onMount } from "svelte";
  import {
    selectedMemberStore,
    selectedMemberDisplayName,
    selectedMemberShortName,
    isSelectedMemberVip,
    selectedMemberCardType,
    memberOptions,
    selectMember,
    loadMemberList,
  } from "../stores/memberStore.js";
  import {
    getMemberCardTypeDisplay,
    searchMembers,
  } from "../utils/memberData.js";

  // Component props
  export let compact = false; // Whether to show compact version
  export let showCardType = true; // Whether to show card type
  export let showVipIndicator = true; // Whether to show VIP indicator

  // Component state
  let isOpen = false;
  let searchTerm = "";
  let dropdownElement;
  let searchInput;
  let selectedIndex = -1;

  // Reactive filtered options using the searchMembers utility for Thai name support
  $: filteredOptions = (() => {
    if (!searchTerm.trim()) {
      return $memberOptions;
    }

    // Use the searchMembers utility to filter members, then map to options format
    const filteredMembers = searchMembers(searchTerm, false);
    const filteredMemberCodes = new Set(
      filteredMembers.map((m) => m.memberCode),
    );

    return $memberOptions.filter((option) =>
      filteredMemberCodes.has(option.value),
    );
  })();

  // Handle member selection
  function handleMemberSelect(memberCode) {
    selectMember(memberCode);
    closeDropdown();
  }

  // Open dropdown
  function openDropdown() {
    isOpen = true;
    searchTerm = "";
    selectedIndex = -1;

    // Focus search input after dropdown opens
    setTimeout(() => {
      if (searchInput) {
        searchInput.focus();
      }
    }, 10);
  }

  // Close dropdown
  function closeDropdown() {
    isOpen = false;
    searchTerm = "";
    selectedIndex = -1;
  }

  // Handle keyboard navigation
  function handleKeydown(event) {
    if (!isOpen) {
      if (event.key === "Enter" || event.key === " ") {
        event.preventDefault();
        openDropdown();
      }
      return;
    }

    switch (event.key) {
      case "Escape":
        event.preventDefault();
        closeDropdown();
        break;

      case "ArrowDown":
        event.preventDefault();
        selectedIndex = Math.min(selectedIndex + 1, filteredOptions.length - 1);
        break;

      case "ArrowUp":
        event.preventDefault();
        selectedIndex = Math.max(selectedIndex - 1, -1);
        break;

      case "Enter":
        event.preventDefault();
        if (selectedIndex >= 0 && filteredOptions[selectedIndex]) {
          handleMemberSelect(filteredOptions[selectedIndex].value);
        }
        break;
    }
  }

  // Handle click outside to close dropdown
  function handleClickOutside(event) {
    if (dropdownElement && !dropdownElement.contains(event.target)) {
      closeDropdown();
    }
  }

  // Initialize component
  onMount(async () => {
    // Load member list if not already loaded
    try {
      await loadMemberList();
    } catch (error) {
      console.error("Failed to load member list in MemberSelector:", error);
    }

    // Add click outside listener
    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  });
</script>

<!-- Member Selector Dropdown -->
<div
  class="relative"
  bind:this={dropdownElement}
  role="combobox"
  aria-expanded={isOpen}
  aria-haspopup="listbox"
  aria-controls="member-listbox"
  aria-label="Select member"
>
  <!-- Trigger Button -->
  <button
    type="button"
    class="inline-flex items-center justify-between w-full px-3 py-2
           bg-white border border-gray-300 rounded-md shadow-sm
           text-sm font-medium text-gray-700
           hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
           transition-colors duration-200
           {compact ? 'min-w-[180px]' : 'min-w-[220px]'}"
    on:click={openDropdown}
    on:keydown={handleKeydown}
    aria-label="Select member: {$selectedMemberDisplayName ||
      'No member selected'}"
  >
    <div class="flex items-center space-x-2 min-w-0 flex-1">
      <!-- Member Info -->
      {#if $selectedMemberStore}
        <div class="flex items-center space-x-2 min-w-0">
          <!-- VIP Indicator -->
          <!-- {#if showVipIndicator && $isSelectedMemberVip}
            <span class="text-yellow-500 text-xs" aria-label="VIP member"
              >⭐</span
            >
          {/if} -->

          <!-- Member Name -->
          <span class="truncate">
            {compact ? $selectedMemberShortName : $selectedMemberDisplayName}
          </span>

          <!-- Card Type -->
          {#if showCardType && $selectedMemberCardType}
            <span class="text-xs text-gray-500 hidden sm:inline">
              {$selectedMemberCardType}
            </span>
          {/if}
        </div>
      {:else}
        <span class="text-gray-500">Select Member</span>
      {/if}
    </div>

    <!-- Dropdown Arrow -->
    <svg
      class="ml-2 h-4 w-4 text-gray-400 transition-transform duration-200 {isOpen
        ? 'rotate-180'
        : ''}"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      aria-hidden="true"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M19 9l-7 7-7-7"
      />
    </svg>
  </button>

  <!-- Dropdown Menu -->
  {#if isOpen}
    <div
      id="member-listbox"
      class="absolute z-50 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg
             max-h-60 overflow-auto
             {compact ? 'min-w-[280px]' : 'min-w-[320px]'}"
      role="listbox"
      aria-label="Member list"
    >
      <!-- Search Input -->
      <div class="p-2 border-b border-gray-200">
        <input
          bind:this={searchInput}
          bind:value={searchTerm}
          type="text"
          placeholder="Search members..."
          class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md
                 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          on:keydown={handleKeydown}
          aria-label="Search members"
        />
      </div>

      <!-- Member Options -->
      <div class="py-1">
        {#if filteredOptions.length === 0}
          <div class="px-3 py-2 text-sm text-gray-500 text-center">
            No members found
          </div>
        {:else}
          {#each filteredOptions as option, index}
            <button
              type="button"
              class="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none
                     flex items-center justify-between
                     {selectedIndex === index ? 'bg-gray-100' : ''}
                     {option.value === $selectedMemberStore?.memberCode
                ? 'bg-blue-50 text-blue-700'
                : 'text-gray-900'}"
              on:click={() => handleMemberSelect(option.value)}
              role="option"
              aria-selected={option.value === $selectedMemberStore?.memberCode}
            >
              <div class="flex items-center space-x-2 min-w-0 flex-1">
                <!-- VIP Indicator -->
                <!-- {#if option.vip}
                  <span class="text-yellow-500 text-xs" aria-label="VIP member"
                    >⭐</span
                  >
                {/if} -->

                <!-- Member Info -->
                <div class="min-w-0 flex-1">
                  <div class="truncate font-medium">
                    {option.label}
                  </div>
                  <div class="text-xs text-gray-500 truncate">
                    {option.value} • {option.member.email || "No email"}
                  </div>
                </div>
              </div>

              <!-- Card Type -->
              <div class="ml-2 text-xs text-gray-500">
                {getMemberCardTypeDisplay(option.member)}
              </div>
            </button>
          {/each}
        {/if}
      </div>
    </div>
  {/if}
</div>
