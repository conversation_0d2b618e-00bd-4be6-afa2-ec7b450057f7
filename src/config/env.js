/**
 * Environment Configuration
 * 
 * This module provides access to environment variables for the TPA API integration.
 * It uses Vite's built-in environment variable handling.
 * 
 * Note: In Vite, only variables prefixed with VITE_ are exposed to the client.
 * Server-side variables (like TPA_API_*) need to be handled differently.
 */

// Client-side environment variables (prefixed with VITE_)
// Handle cases where import.meta.env might be undefined (e.g., in Node.js or SSR)
let env = {};
try {
  env = import.meta.env || {};
} catch (error) {
  // import.meta.env is not available (e.g., in Node.js)
  env = {};
}

export const clientConfig = {
  devMode: env.VITE_DEV_MODE === 'true',
  apiEnvironment: env.VITE_API_ENVIRONMENT || 'development',
  mode: env.MODE || 'development',
  isDev: env.DEV !== false,
  isProd: env.PROD === true
};

// TPA API Configuration
// These will be loaded from environment variables during build time
// For now, we'll use default values that match our .env file
export const tpaApiConfig = {
  baseUrl: env.VITE_TPA_API_BASE_URL || 'http://localhost:9000',
  prefix: env.VITE_TPA_API_PREFIX || '/api',
  timeout: parseInt(env.VITE_TPA_API_TIMEOUT || '30000'),
  retryAttempts: parseInt(env.VITE_TPA_API_RETRY_ATTEMPTS || '3')
};

// Full API base URL
export const getApiBaseUrl = () => {
  return `${tpaApiConfig.baseUrl}${tpaApiConfig.prefix}`;
};

// Environment validation
export const validateEnvironment = () => {
  const errors = [];

  if (!tpaApiConfig.baseUrl) {
    errors.push('TPA_API_BASE_URL is required');
  }

  if (!tpaApiConfig.prefix) {
    errors.push('TPA_API_PREFIX is required');
  }

  if (isNaN(tpaApiConfig.timeout) || tpaApiConfig.timeout <= 0) {
    errors.push('TPA_API_TIMEOUT must be a positive number');
  }

  if (isNaN(tpaApiConfig.retryAttempts) || tpaApiConfig.retryAttempts < 0) {
    errors.push('TPA_API_RETRY_ATTEMPTS must be a non-negative number');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Log configuration in development mode
if (clientConfig.devMode && clientConfig.isDev) {
  console.log('Environment Configuration:', {
    client: clientConfig,
    tpaApi: tpaApiConfig,
    fullApiUrl: getApiBaseUrl()
  });

  const validation = validateEnvironment();
  if (!validation.isValid) {
    console.warn('Environment validation errors:', validation.errors);
  }
}
