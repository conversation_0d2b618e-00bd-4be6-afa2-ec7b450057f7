/**
 * Data Formatting Utilities
 * 
 * Utilities for formatting API response data into consistent structures
 * that match the existing application's data models.
 */

import { PolicyStatus, ClaimStatus, MemberType, CardType } from '../api/types.js';

/**
 * Format policy data from API response to application format
 * @param {Object} apiPolicy - Policy data from API
 * @returns {Object} Formatted policy data
 */
export function formatPolicy(apiPolicy) {
  if (!apiPolicy) return null;

  return {
    id: apiPolicy.MemberCode || apiPolicy.PolicyNo || generateId(),
    policyNumber: apiPolicy.PolicyNo || 'N/A',
    type: mapPolicyType(apiPolicy.PolicyType || apiPolicy.InsuranceType),
    coverageAmount: parseFloat(apiPolicy.CoverageAmount || apiPolicy.SumInsured || 0),
    monthlyPremium: parseFloat(apiPolicy.Premium || apiPolicy.MonthlyPremium || 0),
    status: mapPolicyStatus(apiPolicy.Status || apiPolicy.PolicyStatus),
    expirationDate: formatDate(apiPolicy.ExpiryDate || apiPolicy.EndDate),
    effectiveDate: formatDate(apiPolicy.EffectiveDate || apiPolicy.StartDate),
    renewalDate: formatDate(apiPolicy.RenewalDate || apiPolicy.ExpiryDate),
    description: apiPolicy.Description || generatePolicyDescription(apiPolicy),
    insurer: apiPolicy.InsurerName || mapInsurerCode(apiPolicy.InsurerCode),

    // Additional fields from API
    memberCode: apiPolicy.MemberCode,
    citizenId: apiPolicy.CitizenID,
    memberType: apiPolicy.MemberType,
    cardType: apiPolicy.CardType,
    vipStatus: apiPolicy.VIPStatus === 'Y',
    language: apiPolicy.Language,

    // Member information
    member: {
      nameTh: apiPolicy.NameTH,
      nameEn: apiPolicy.NameEN,
      citizenId: apiPolicy.CitizenID,
      memberCode: apiPolicy.MemberCode,
      memberType: apiPolicy.MemberType,
      cardType: apiPolicy.CardType,
      vipStatus: apiPolicy.VIPStatus === 'Y',
      language: apiPolicy.Language
    }
  };
}

/**
 * Format claim data from API response to application format
 * @param {Object} apiClaim - Claim data from API
 * @returns {Object} Formatted claim data
 */
export function formatClaim(apiClaim) {
  if (!apiClaim) return null;

  return {
    id: apiClaim.ClaimID || apiClaim.ClaimNo || generateId(),
    claimNumber: apiClaim.ClaimNo || 'N/A',
    type: mapClaimType(apiClaim.ClaimType || apiClaim.ServiceType),
    status: mapClaimStatus(apiClaim.Status || apiClaim.ClaimStatus),
    amount: parseFloat(apiClaim.ClaimAmount || apiClaim.Amount || 0),
    approvedAmount: parseFloat(apiClaim.ApprovedAmount || apiClaim.PaidAmount || 0),
    deductible: parseFloat(apiClaim.Deductible || 0),
    dateReported: formatDate(apiClaim.ClaimDate || apiClaim.ReportDate),
    dateOfLoss: formatDate(apiClaim.IncidentDate || apiClaim.ServiceDate),
    dateResolved: formatDate(apiClaim.SettlementDate || apiClaim.PaidDate),
    description: apiClaim.Description || apiClaim.ServiceDescription || 'N/A',
    policyNumber: apiClaim.PolicyNo || 'N/A',

    // Policy information
    policy: {
      id: apiClaim.PolicyNo || apiClaim.MemberCode,
      number: apiClaim.PolicyNo,
      type: mapPolicyType(apiClaim.PolicyType),
      holder: apiClaim.NameEN || apiClaim.NameTH
    },

    // Additional API fields
    memberCode: apiClaim.MemberCode,
    serviceType: apiClaim.ServiceType,
    providerName: apiClaim.ProviderName,
    diagnosis: apiClaim.Diagnosis,

    // Adjuster information (if available)
    adjuster: apiClaim.AdjusterName ? {
      name: apiClaim.AdjusterName,
      phone: apiClaim.AdjusterPhone || 'N/A',
      email: apiClaim.AdjusterEmail || 'N/A',
      id: apiClaim.AdjusterID || 'N/A'
    } : null
  };
}

/**
 * Format policy detail response with all sections
 * @param {Object} apiResponse - Full API response
 * @returns {Object} Formatted policy detail
 */
export function formatPolicyDetail(apiResponse) {
  if (!apiResponse) return null;

  const policy = apiResponse.Policy || apiResponse;
  const benefits = apiResponse.Benefits || [];
  const conditions = apiResponse.Conditions || [];
  const claims = apiResponse.Claims || [];

  return {
    policy: formatPolicy(policy),
    benefits: benefits.map(formatBenefit),
    conditions: conditions.map(formatCondition),
    claims: claims.map(formatClaim),

    // Summary information
    summary: {
      totalBenefits: benefits.length,
      totalClaims: claims.length,
      totalClaimAmount: claims.reduce((sum, claim) => sum + parseFloat(claim.ClaimAmount || 0), 0),
      activeClaims: claims.filter(claim => ['Open', 'Pending'].includes(claim.Status)).length
    }
  };
}

/**
 * Format benefit data
 * @param {Object} apiBenefit - Benefit data from API
 * @returns {Object} Formatted benefit
 */
export function formatBenefit(apiBenefit) {
  if (!apiBenefit) return null;

  return {
    id: apiBenefit.BenefitID || generateId(),
    name: apiBenefit.BenefitName || 'N/A',
    description: apiBenefit.Description || apiBenefit.BenefitDescription,
    amount: parseFloat(apiBenefit.Amount || apiBenefit.BenefitAmount || 0),
    limit: parseFloat(apiBenefit.Limit || apiBenefit.MaxAmount || 0),
    used: parseFloat(apiBenefit.Used || apiBenefit.UsedAmount || 0),
    remaining: parseFloat(apiBenefit.Remaining || 0),
    type: apiBenefit.BenefitType || 'General',
    unit: apiBenefit.Unit || 'THB'
  };
}

/**
 * Format condition data
 * @param {Object} apiCondition - Condition data from API
 * @returns {Object} Formatted condition
 */
export function formatCondition(apiCondition) {
  if (!apiCondition) return null;

  return {
    id: apiCondition.ConditionID || generateId(),
    title: apiCondition.Title || apiCondition.ConditionName,
    description: apiCondition.Description || apiCondition.Details,
    type: apiCondition.Type || apiCondition.ConditionType || 'General',
    isActive: apiCondition.IsActive !== false,
    effectiveDate: formatDate(apiCondition.EffectiveDate),
    expiryDate: formatDate(apiCondition.ExpiryDate)
  };
}

/**
 * Map policy type from API to application format
 * @param {string} apiType - Policy type from API
 * @returns {string} Mapped policy type
 */
function mapPolicyType(apiType) {
  const typeMap = {
    'AUTO': 'Auto',
    'MOTOR': 'Auto',
    'HOME': 'Home',
    'PROPERTY': 'Home',
    'HEALTH': 'Health',
    'MEDICAL': 'Health',
    'LIFE': 'Life',
    'BUSINESS': 'Business',
    'COMMERCIAL': 'Business'
  };

  return typeMap[apiType?.toUpperCase()] || apiType || 'General';
}

/**
 * Map claim type from API to application format
 * @param {string} apiType - Claim type from API
 * @returns {string} Mapped claim type
 */
function mapClaimType(apiType) {
  const typeMap = {
    'AUTO': 'Auto',
    'MOTOR': 'Auto',
    'HOME': 'Home',
    'PROPERTY': 'Home',
    'HEALTH': 'Health',
    'MEDICAL': 'Health',
    'OUTPATIENT': 'Health',
    'INPATIENT': 'Health',
    'DENTAL': 'Health',
    'LIFE': 'Life'
  };

  return typeMap[apiType?.toUpperCase()] || apiType || 'General';
}

/**
 * Map policy status from API to application format
 * @param {string} apiStatus - Status from API
 * @returns {string} Mapped status
 */
function mapPolicyStatus(apiStatus) {
  const statusMap = {
    'ACTIVE': 'Active',
    'INACTIVE': 'Inactive',
    'EXPIRED': 'Expired',
    'PENDING': 'Pending',
    'CANCELLED': 'Cancelled',
    'SUSPENDED': 'Inactive'
  };

  return statusMap[apiStatus?.toUpperCase()] || apiStatus || 'Unknown';
}

/**
 * Map claim status from API to application format
 * @param {string} apiStatus - Status from API
 * @returns {string} Mapped status
 */
function mapClaimStatus(apiStatus) {
  const statusMap = {
    'APPROVED': 'Approved',
    'AUTHORIZED': 'Authorized',
    'OPEN': 'Open',
    'PAID': 'Paid',
    'PENDING': 'Pending',
    'PENDING FOR APPROVAL': 'Pending',
    'REJECTED': 'Rejected',
    'DENIED': 'Rejected',
    'CLOSED': 'Paid'
  };

  return statusMap[apiStatus?.toUpperCase()] || apiStatus || 'Unknown';
}

/**
 * Map insurer code to company name
 * @param {string} code - Insurer code
 * @returns {string} Company name
 */
function mapInsurerCode(code) {
  const insurerMap = {
    'INS001': 'บริษัท ประกันภัย เอ จำกัด (Insurance Company A Ltd.)',
    'INS002': 'Health Insurance Co. Ltd.',
    'INS003': 'บริษัท ประกันชีวิต ซี จำกัด (Life Insurance C Ltd.)'
  };

  return insurerMap[code] || code || 'Unknown Insurer';
}

/**
 * Format date string to ISO format
 * @param {string} dateStr - Date string from API
 * @returns {string} ISO formatted date or null
 */
function formatDate(dateStr) {
  if (!dateStr) return null;

  try {
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date.toISOString();
  } catch {
    return null;
  }
}

/**
 * Generate policy description based on available data
 * @param {Object} policy - Policy data
 * @returns {string} Generated description
 */
function generatePolicyDescription(policy) {
  const type = mapPolicyType(policy.PolicyType || policy.InsuranceType);
  const coverage = policy.CoverageAmount || policy.SumInsured;

  if (coverage) {
    return `${type} insurance policy with coverage amount of ${formatCurrency(coverage)}`;
  }

  return `${type} insurance policy`;
}

/**
 * Format currency amount
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code (default: THB)
 * @returns {string} Formatted currency
 */
export function formatCurrency(amount, currency = 'THB') {
  if (isNaN(amount)) return 'N/A';

  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(amount);
}

/**
 * Generate unique ID
 * @returns {string} Unique ID
 */
function generateId() {
  return `id_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
