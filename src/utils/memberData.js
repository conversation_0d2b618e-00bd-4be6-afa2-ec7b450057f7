/**
 * Member Data Utilities
 * 
 * Utilities for parsing and managing member data from CSV source.
 * This module handles the conversion of CSV data to structured member objects
 * and provides helper functions for member data manipulation.
 */

import { TypeCheckers } from '../api/types.js';

/**
 * Raw member data aligned with new data structure from AVAILABLE_DATA.md
 * Contains only essential fields needed for API calls:
 * - citizenID: For API authentication and data fetching
 * - insurerCode: For API parameter requirements
 * - memberCode: For specific member operations
 *
 * All other data fields can be fetched dynamically from API endpoints.
 * Maintains backward compatibility with existing component usage.
 */
const RAW_MEMBER_DATA = [
  // Citizen CIT001 (สมชาย ใจดี / <PERSON><PERSON><PERSON><PERSON>) - 3 members
  {
    memberCode: 'MEM001',
    citizenID: 'CIT001',
    insurerCode: 'INS001'
  },
  {
    memberCode: 'MEM002',
    citizenID: 'CIT001',
    insurerCode: 'INS002'
  },
  {
    memberCode: 'MEM003',
    citizenID: 'CIT001',
    insurerCode: 'INS003'
  },
  // Citizen CIT002 (มาลี สวยงาม / <PERSON><PERSON>) - 2 members
  {
    memberCode: 'MEM004',
    citizenID: 'CIT002',
    insurerCode: 'INS001'
  },
  {
    memberCode: 'MEM005',
    citizenID: 'CIT002',
    insurerCode: 'INS002'
  },
  // Citizen CIT003 (ปิยะดา สุขใส / Piyada Suksai) - 3 members
  {
    memberCode: 'MEM006',
    citizenID: 'CIT003',
    insurerCode: 'INS001'
  },
  {
    memberCode: 'MEM007',
    citizenID: 'CIT003',
    insurerCode: 'INS002'
  },
  {
    memberCode: 'MEM008',
    citizenID: 'CIT003',
    insurerCode: 'INS003'
  }
];

/**
 * Enhance member data with display information for backward compatibility
 * Maps citizen IDs to display names and adds default values for missing fields
 * @param {Object} member - Basic member object with essential fields
 * @returns {Object} Enhanced member object with display data
 */
function enhanceMemberData(member) {
  // Mapping from citizen IDs to display information based on AVAILABLE_DATA.md
  const citizenDisplayData = {
    'CIT001': {
      titleTH: 'นาย',
      nameTH: 'สมชาย',
      surnameTH: 'ใจดี',
      titleEN: 'Mr.',
      nameEN: 'Somchai',
      surnameEN: 'Jaidee'
    },
    'CIT002': {
      titleTH: 'นาง',
      nameTH: 'มาลี',
      surnameTH: 'สวยงาม',
      titleEN: 'Mrs.',
      nameEN: 'Malee',
      surnameEN: 'Suayngam'
    },
    'CIT003': {
      titleTH: 'นางสาว',
      nameTH: 'ปิยะดา',
      surnameTH: 'สุขใส',
      titleEN: 'Ms.',
      nameEN: 'Piyada',
      surnameEN: 'Suksai'
    }
  };

  const displayData = citizenDisplayData[member.citizenID] || {
    titleTH: 'นาย',
    nameTH: 'ไม่ระบุ',
    surnameTH: 'ชื่อ',
    titleEN: 'Mr.',
    nameEN: 'Unknown',
    surnameEN: 'Name'
  };

  // Return enhanced member object with backward compatibility
  return {
    ...member,
    ...displayData,
    memberStatus: 'Active', // All members in new structure are active
    memberType: 'Principal', // Default member type
    principleMemberCode: member.memberCode,
    principleName: `${displayData.nameTH} ${displayData.surnameTH}`,
    vip: 'N', // Default VIP status - will be fetched from API when needed
    vipRemarks: '',
    cardType: 'Standard', // Default card type - will be fetched from API when needed
    language: 'TH', // Default language preference
    birthDate: null, // Will be fetched from API when needed
    gender: null, // Will be fetched from API when needed
    citizenship: 'Thai', // Default citizenship
    countryCode: 'TH', // Default country code
    mobile: null, // Will be fetched from API when needed
    email: null // Will be fetched from API when needed
  };
}

/**
 * Get all available members with enhanced data for backward compatibility
 * @param {boolean} includeInactive - Whether to include inactive members
 * @returns {Array} Array of member objects with enhanced display data
 */
export function getAllMembers(includeInactive = false) {
  // All members in the new structure are active by default
  // Enhanced with display data for backward compatibility
  // Note: includeInactive parameter kept for API compatibility but not used
  return RAW_MEMBER_DATA.map(member => enhanceMemberData(member));
}

/**
 * Get member by member code with enhanced data
 * @param {string} memberCode - Member code to search for
 * @returns {Object|null} Enhanced member object or null if not found
 */
export function getMemberByCode(memberCode) {
  const member = RAW_MEMBER_DATA.find(member => member.memberCode === memberCode);
  return member ? enhanceMemberData(member) : null;
}

/**
 * Get member display name - always returns Thai name for consistency
 * @param {Object} member - Member object
 * @param {string} language - Language preference (kept for compatibility, but always returns Thai)
 * @returns {string} Formatted display name in Thai
 */
export function getMemberDisplayName(member, language = 'TH') {
  if (!member) return '';

  // Always use Thai names for consistent interface
  // Note: language parameter kept for API compatibility but not used
  if (member.titleTH && member.nameTH && member.surnameTH) {
    return `${member.titleTH}${member.nameTH} ${member.surnameTH}`;
  }

  // Fallback to English if Thai is not available (rare case)
  if (member.titleEN && member.nameEN && member.surnameEN) {
    return `${member.titleEN} ${member.nameEN} ${member.surnameEN}`;
  }

  // Final fallback to member code if names are not available
  return member.memberCode || 'Unknown Member';
}

/**
 * Get member short name (first name only) - always returns Thai name for consistency
 * @param {Object} member - Member object
 * @param {string} language - Language preference (kept for compatibility, but always returns Thai)
 * @returns {string} Short name in Thai
 */
export function getMemberShortName(member, language = 'TH') {
  if (!member) return '';

  // Always use Thai names for consistent interface
  // Note: language parameter kept for API compatibility but not used
  if (member.nameTH) {
    return member.nameTH;
  }

  // Fallback to English if Thai is not available (rare case)
  if (member.nameEN) {
    return member.nameEN;
  }

  return member.memberCode || 'Unknown';
}

/**
 * Get members for dropdown display - always uses Thai names for consistency
 * @param {boolean} includeInactive - Whether to include inactive members
 * @returns {Array} Array of member options for dropdown
 */
export function getMemberOptions(includeInactive = false) {
  const members = getAllMembers(includeInactive);

  return members.map(member => ({
    value: member.memberCode,
    label: getMemberDisplayName(member, 'TH'), // Always use Thai for consistency
    shortLabel: getMemberShortName(member, 'TH'), // Always use Thai for consistency
    status: member.memberStatus,
    vip: member.vip === 'Y',
    cardType: member.cardType,
    memberType: member.memberType,
    language: member.language,
    member: member // Include full member object for reference
  }));
}

/**
 * Filter members by search term - prioritizes Thai names for consistency
 * @param {string} searchTerm - Search term to filter by
 * @param {boolean} includeInactive - Whether to include inactive members
 * @returns {Array} Filtered array of enhanced members
 */
export function searchMembers(searchTerm, includeInactive = false) {
  if (!searchTerm || searchTerm.trim() === '') {
    return getAllMembers(includeInactive);
  }

  const term = searchTerm.toLowerCase().trim();
  const members = getAllMembers(includeInactive);

  return members.filter(member => {
    // Search in essential fields and enhanced display data
    const searchFields = [
      member.memberCode,
      member.citizenID,
      member.insurerCode,
      member.titleTH,
      member.nameTH,
      member.surnameTH,
      member.nameEN, // Keep English as fallback for search
      member.surnameEN,
      member.email,
      member.mobile
    ];

    return searchFields.some(field =>
      field && field.toString().toLowerCase().includes(term)
    );
  });
}

/**
 * Validate member data structure
 * @param {Object} member - Member object to validate
 * @returns {Object} Validation result
 */
export function validateMemberData(member) {
  return TypeCheckers.validateMember(member);
}

/**
 * Get default member (first active member)
 * @returns {Object|null} Default member object
 */
export function getDefaultMember() {
  const activeMembers = getAllMembers(false);
  return activeMembers.length > 0 ? activeMembers[0] : null;
}

/**
 * Check if member is VIP
 * @param {Object} member - Member object
 * @returns {boolean} Whether member is VIP
 */
export function isMemberVip(member) {
  return member && member.vip === 'Y';
}

/**
 * Get member's card type display
 * @param {Object} member - Member object
 * @returns {string} Card type with emoji
 */
export function getMemberCardTypeDisplay(member) {
  if (!member || !member.cardType) return '';

  const cardTypeEmojis = {
    'Standard': '🟢',
    'Gold': '🟡',
    'Platinum': '⚪',
    'Diamond': '💎'
  };

  const emoji = cardTypeEmojis[member.cardType] || '';
  return `${emoji} ${member.cardType}`;
}
