# TPA API Client Documentation

## Overview

The TPA API Client provides a comprehensive, type-safe interface for interacting with the Third Party Administrator (TPA) API endpoints. It includes automatic retry logic, parameter validation, error handling, and response formatting.

## Quick Start

```javascript
import api from './src/api/index.js';

// Search policies by citizen ID
const policies = await api.policies.searchByCitizenId('1234567890123');

// Get policy details for a member
const policyDetail = await api.members.getPolicyDetail('MEM001');

// Get claims for a member
const claims = await api.members.getClaims('MEM001');

// Check API health
const health = await api.system.health();
```

## Installation and Setup

The API client is already configured to work with your environment variables. Ensure your `.env` file contains:

```bash
VITE_TPA_API_BASE_URL=http://localhost:9000
VITE_TPA_API_PREFIX=/api
VITE_TPA_API_TIMEOUT=30000
VITE_TPA_API_RETRY_ATTEMPTS=3
```

## Core Features

### 1. Automatic Retry Logic
- Exponential backoff with jitter
- Configurable retry attempts (default: 3)
- Retries on network errors and server errors (5xx)
- No retry on client errors (4xx)

### 2. Parameter Validation
- Validates parameter combinations according to TPA API requirements
- Type checking for all parameter values
- Automatic parameter cleaning (removes null/empty values)
- Detailed validation error messages

### 3. Error Handling
- Custom error classes for different error types
- User-friendly error messages
- Comprehensive error logging
- Structured error responses

### 4. Response Formatting
- Consistent response structure across all endpoints
- Data transformation to match application models
- Automatic type conversion and formatting

## API Reference

### Policy Operations

#### `searchPolicies(params, options)`
Search for insurance policies using various parameter combinations.

**Parameters:**
- `params` (Object): Search parameters (must match one valid combination)
- `options` (Object): Additional request options

**Valid Parameter Combinations:**
1. `INSURER_CODE` + `CITIZEN_ID`
2. `INSURER_CODE` + `POLICY_NO` + `NAME_TH`
3. `INSURER_CODE` + `POLICY_NO` + `NAME_EN`
4. `INSURER_CODE` + `CERTIFICATE_NO` + `NAME_TH`
5. `INSURER_CODE` + `CERTIFICATE_NO` + `NAME_EN`
6. `INSURER_CODE` + `STAFF_NO` + `NAME_TH`
7. `INSURER_CODE` + `STAFF_NO` + `NAME_EN`
8. `INSURER_CODE` + `OTHER_ID`
9. `INSURER_CODE` + `NAME_TH`
10. `INSURER_CODE` + `NAME_EN`

**Example:**
```javascript
// Search by insurer code and citizen ID
const result = await searchPolicies({
  INSURER_CODE: 'INS001',
  CITIZEN_ID: '1234567890123'
});

// Search by insurer code and English name
const result = await searchPolicies({
  INSURER_CODE: 'INS002',
  NAME_EN: 'Wichai Kengmak'
});
```

**Response:**
```javascript
{
  success: true,
  data: [/* array of policies */],
  total: 1,
  params: { /* cleaned parameters */ }
}
```

#### `getPolicyDetail(memberCode, options)`
Get detailed policy information for a specific member.

**Parameters:**
- `memberCode` (String): Member code (e.g., 'MEM001')
- `options` (Object): Additional request options

**Example:**
```javascript
const policyDetail = await getPolicyDetail('MEM001');
```

**Response:**
```javascript
{
  success: true,
  data: {
    Policy: { /* policy information */ },
    Benefits: [/* array of benefits */],
    Conditions: [/* array of conditions */],
    Claims: [/* array of claims */]
  },
  memberCode: 'MEM001'
}
```

### Claim Operations

#### `getClaimsList(params, options)`
List claims history for a member.

**Parameters:**
- `params` (Object): Search parameters
- `options` (Object): Additional request options

**Valid Parameter Combinations:**
1. `MEMBER_CODE`
2. `INSURER_CODE` + `CITIZEN_ID`

**Example:**
```javascript
// Get claims by member code
const claims = await getClaimsList({ MEMBER_CODE: 'MEM001' });

// Get claims by insurer code and citizen ID
const claims = await getClaimsList({
  INSURER_CODE: 'INS001',
  CITIZEN_ID: '1234567890123'
});
```

### Convenience Functions

#### `searchPoliciesByCitizenId(citizenId, insurerCode, options)`
Simplified policy search by citizen ID.

```javascript
const policies = await searchPoliciesByCitizenId('1234567890123', 'INS001');
```

#### `getMemberData(memberCode, options)`
Get both policy details and claims for a member in a single call.

```javascript
const memberData = await getMemberData('MEM001');
// Returns: { policy: {...}, claims: [...], memberCode: 'MEM001' }
```

### System Operations

#### `checkHealth(options)`
Check API health status.

```javascript
const health = await checkHealth();
```

#### `getApiInfo(options)`
Get API information and available endpoints.

```javascript
const info = await getApiInfo();
```

## Error Handling

### Error Types

1. **ValidationError**: Invalid parameters or parameter combinations
2. **ApiError**: HTTP errors from the API (4xx, 5xx)
3. **NetworkError**: Network connectivity issues
4. **TimeoutError**: Request timeout
5. **AuthenticationError**: Authentication failures (401)
6. **AuthorizationError**: Permission denied (403)
7. **RateLimitError**: Too many requests (429)

### Error Handling Example

```javascript
try {
  const policies = await searchPolicies({
    INSURER_CODE: 'INS001',
    CITIZEN_ID: '1234567890123'
  });
} catch (error) {
  if (error instanceof ValidationError) {
    console.log('Invalid parameters:', error.getUserMessage());
  } else if (error instanceof NetworkError) {
    console.log('Network issue:', error.getUserMessage());
  } else if (error instanceof ApiError) {
    console.log('API error:', error.getUserMessage());
    console.log('Status:', error.status);
  }
}
```

## Configuration

### Custom Client Configuration

```javascript
import { TpaApiClient } from './src/api/client.js';

const customClient = new TpaApiClient({
  baseUrl: 'https://custom-api.example.com/api',
  timeout: 60000,
  retryAttempts: 5,
  retryDelay: 2000
});
```

### Request/Response Interceptors

```javascript
import { defaultClient } from './src/api/client.js';

// Add request interceptor
defaultClient.addRequestInterceptor((config) => {
  config.headers['X-Custom-Header'] = 'value';
  return config;
});

// Add response interceptor
defaultClient.addResponseInterceptor((response) => {
  console.log('Response received:', response.status);
  return response;
});
```

## Data Formatting

The API client automatically formats response data to match your application's data models:

### Policy Formatting
```javascript
// API Response -> Application Format
{
  MemberCode: 'MEM001',
  PolicyNo: 'POL001',
  InsurerCode: 'INS001',
  // ... other API fields
}
// Becomes:
{
  id: 'MEM001',
  policyNumber: 'POL001',
  insurer: 'Insurance Company A Ltd.',
  // ... formatted fields
}
```

### Custom Formatting
```javascript
import { formatPolicy, formatClaim } from './src/utils/formatters.js';

const formattedPolicy = formatPolicy(apiPolicyData);
const formattedClaim = formatClaim(apiClaimData);
```

## Testing

### Running Tests
```bash
npm test                # Run all tests
npm run test:ui         # Run tests with UI
npm run test:run        # Run tests once
```

### Test Coverage
- HTTP client functionality
- Endpoint wrapper functions
- Parameter validation
- Error handling
- Data formatting
- Retry logic

### Mock Data
Test utilities provide sample data for testing:

```javascript
import { samplePolicyData, sampleClaimData } from './tests/setup.js';
```

## Best Practices

1. **Always handle errors**: Use try-catch blocks for all API calls
2. **Validate parameters**: Use the validation utilities before making requests
3. **Use convenience functions**: Prefer `searchPoliciesByCitizenId` over manual parameter construction
4. **Check response success**: Always check the `success` field in responses
5. **Log errors appropriately**: Use the built-in error logging for debugging

## Migration from Mock Data

When migrating components from mock data to API calls:

1. Replace mock data arrays with API calls
2. Add loading states to handle async operations
3. Implement error handling UI
4. Update tests to use API mocks
5. Ensure proper data formatting

Example migration:
```javascript
// Before (mock data)
const policies = [/* mock data */];

// After (API integration)
import { searchPoliciesByCitizenId } from './api/index.js';

let policies = [];
let loading = true;
let error = null;

try {
  const result = await searchPoliciesByCitizenId('1234567890123');
  policies = result.data;
} catch (err) {
  error = err.getUserMessage();
} finally {
  loading = false;
}
```
